#!/usr/bin/env python3
"""
OCR preprocessing and text extraction pipeline using OpenCV and Tesseract.

- Loads images and PDF files from a given path (file or directory)
- Applies robust preprocessing for OCR on photos/scans of varying quality
- Saves processed, binarized images suitable for OCR engines
- Extracts text using Tesseract OCR and saves to text files
- Supports Czech and English text recognition

Dependencies: opencv-python, numpy, pytesseract, pdf2image (optional)
"""
from __future__ import annotations

# ==== USER CONFIG (adjust here when running from IDE) ====
# General I/O
INPUT_PATH = "../Pics"            # file or directory
OUTPUT_DIR = "../ocr_output"
RECURSIVE = False               # recurse into subfolders if input is a directory

# SECTION 1: DPI normalization to target 300 DPI
S1_ENABLE = True
S1_TARGET_DPI = 300.0
S1_ASSUMED_WIDTH_IN = 8.27      # default A4 width in inches
S1_ASSUMED_HEIGHT_IN = 11.69    # default A4 height in inches
S1_MAX_LONG_SIDE = 0            # optional cap of long side after DPI scaling (0 disables)

# SECTION 2: Document detection + perspective warp
S2_ENABLE = False
S2_MIN_AREA_FRACTION = 0.1      # minimal page area as fraction of image area
S2_CANNY_LOW = 75
S2_CANNY_HIGH = 200
S2_MORPH_KERNEL = 5             # closing kernel size for edges
S2_APPROX_EPS_RATIO = 0.02      # epsilon ratio for approxPolyDP (relative to perimeter)

# SECTION 3: Auto 90° orientation
S3_ENABLE = True

# SECTION 4: Grayscale
S4_ENABLE = True                # if False, downstream steps will convert to gray as needed

# SECTION 5: Illumination correction (use only for images with uneven lighting)
S5_ENABLE = True               # disabled by default - often causes more harm than good
S5_BLUR_REL = 0.05              # Gaussian kernel size = round(max(h,w) * S5_BLUR_REL), forced odd

# SECTION 6: CLAHE (local contrast)
S6_ENABLE = True
S6_CLAHE_CLIP = 3.0
S6_CLAHE_GRID = 8

# SECTION 7: Denoise
S7_ENABLE = True
S7_STRENGTH = 5                # h parameter for fastNlMeansDenoising

# SECTION 8: Binarization
S8_ENABLE = True
S8_METHOD = "sauvola"           # one of: gaussian, mean, otsu, sauvola
S8_BLOCK_SIZE = 25              # odd >= 3
S8_C = 10
S8_SAUVOLA_K = 0.2
S8_SAUVOLA_R = 128.0

# SECTION 9: Morphology
S9_ENABLE = True
S9_OPEN = 1                    # 0 disables opening
S9_CLOSE = 1                   # 0 disables closing
S9_MEDIAN = 3                  # median filter kernel size (0 disables, must be odd)

# SECTION 10: Fine deskew (small-angle rotation) - MOVED TO END
S10_ENABLE = True
S10_MIN_ABS_ANGLE = 1.0          # degrees; below this, skip rotation (increased from 0.3)
S10_HOUGH_THRESHOLD_REL = 0.05   # relative to image height (decreased from 0.1)
S10_HOUGH_THRESHOLD_MIN = 50     # minimum threshold (decreased from 100)

# PDF import settings
PDF_DPI = 300                   # DPI for PDF to image conversion

# OCR settings
OCR_ENABLE = True               # enable OCR text extraction
OCR_LANG = "ces"            # Tesseract language codes (ces=Czech, eng=English)
OCR_PSM = 6                     # Page segmentation mode (6=uniform block of text)
OCR_OEM = 3                     # OCR Engine Mode (3=default, based on what is available)
OCR_AUTO_ORIENT = True          # enable automatic orientation detection and correction

# Debug/logging
SAVE_DEBUG = True
VERBOSE = True

# Run mode
RUN_WITH_CLI = False  # True = use CLI; False = use USER CONFIG above
# ==== END USER CONFIG ====

# ==== USER CONFIG stays above imports for IDE convenience ====
import argparse
import logging
import math
import os
import sys

from dataclasses import dataclass
from pathlib import Path
from typing import Iterable, List, Tuple, Dict

import cv2
import numpy as np

# PDF support (optional import)
try:
    from pdf2image import convert_from_path
    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False
    logging.warning("pdf2image not available. PDF files will be skipped. Install with: pip install pdf2image")

# Tesseract OCR support (optional import)
try:
    import pytesseract
    TESSERACT_SUPPORT = True
except ImportError:
    TESSERACT_SUPPORT = False
    logging.warning("pytesseract not available. OCR text extraction will be skipped. Install with: pip install pytesseract")


SUPPORTED_EXTS = {".png", ".jpg", ".jpeg", ".tif", ".tiff", ".bmp", ".webp"}
SUPPORTED_PDF_EXTS = {".pdf"}


@dataclass
class Options:
    # Geometric / structural options (legacy fields used downstream)
    deskew: bool = True
    max_size: int = 0  # legacy resize (disabled; DPI normalization is used instead)
    detect_document: bool = True
    auto_orient: bool = False

    # Photometric options
    illumination_correction: bool = False
    clahe: bool = False
    clahe_clip: float = 3.0
    clahe_grid: int = 8
    denoise: bool = False
    denoise_strength: int = 10  # h parameter for fastNlMeansDenoising

    # Binarization
    threshold_method: str = "gaussian"  # one of: gaussian, mean, otsu, sauvola
    block_size: int = 31  # odd >= 3
    C: int = 10  # constant subtracted in adaptive thresholding
    sauvola_k: float = 0.2
    sauvola_R: float = 128.0

    # Morphology
    morph_open: int = 1  # kernel size for opening (0 to disable)
    morph_close: int = 1  # kernel size for closing (0 to disable)
    morph_median: int = 3  # median filter kernel size (0 to disable, must be odd)

    # Debug
    save_debug: bool = False

# --- DPI normalization utilities ---

def compute_scale_for_300dpi(img: np.ndarray) -> float:
    h, w = img.shape[:2]
    # If the image already has plausible print resolution, near 300 DPI width, skip heavy scaling
    target_w = int(round(S1_ASSUMED_WIDTH_IN * S1_TARGET_DPI))
    target_h = int(round(S1_ASSUMED_HEIGHT_IN * S1_TARGET_DPI))
    # Choose scale that best matches either dimension while preserving aspect ratio
    scale_w = target_w / float(w)
    scale_h = target_h / float(h)
    # Use the larger scale to reach at least target size in one dimension
    scale = max(scale_w, scale_h)
    # Optional cap on long side
    if S1_MAX_LONG_SIDE and S1_MAX_LONG_SIDE > 0:
        long_after = max(h, w) * scale
        if long_after > S1_MAX_LONG_SIDE:
            scale = S1_MAX_LONG_SIDE / float(max(h, w))
    return float(scale)


def dpi_normalize(img: np.ndarray) -> np.ndarray:
    if not S1_ENABLE:
        return img
    scale = compute_scale_for_300dpi(img)
    if abs(scale - 1.0) < 1e-3:
        return img
    new_w = int(round(img.shape[1] * scale))
    new_h = int(round(img.shape[0] * scale))
    return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_CUBIC if scale > 1.0 else cv2.INTER_AREA)


# --- Document detection and perspective transform utilities ---

def _order_points(pts: np.ndarray) -> np.ndarray:
    # pts: (4,2)
    rect = np.zeros((4, 2), dtype=np.float32)
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]  # top-left
    rect[2] = pts[np.argmax(s)]  # bottom-right
    diff = np.diff(pts, axis=1).reshape(-1)
    rect[1] = pts[np.argmin(diff)]  # top-right
    rect[3] = pts[np.argmax(diff)]  # bottom-left
    return rect


def four_point_warp(image: np.ndarray, pts: np.ndarray) -> np.ndarray:
    rect = _order_points(pts.astype(np.float32))
    (tl, tr, br, bl) = rect
    widthA = np.linalg.norm(br - bl)
    widthB = np.linalg.norm(tr - tl)
    maxWidth = int(round(max(widthA, widthB)))

    heightA = np.linalg.norm(tr - br)
    heightB = np.linalg.norm(tl - bl)
    maxHeight = int(round(max(heightA, heightB)))

    maxWidth = max(32, maxWidth)
    maxHeight = max(32, maxHeight)

    dst = np.array([
        [0, 0],
        [maxWidth - 1, 0],
        [maxWidth - 1, maxHeight - 1],
        [0, maxHeight - 1]
    ], dtype=np.float32)

    M = cv2.getPerspectiveTransform(rect, dst)
    warped = cv2.warpPerspective(image, M, (maxWidth, maxHeight), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
    return warped


def detect_document_and_warp(image_bgr: np.ndarray) -> Tuple[np.ndarray | None, Dict[str, np.ndarray]]:
    debug: Dict[str, np.ndarray] = {}
    img = image_bgr.copy()
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # Blur to suppress noise
    blur = cv2.GaussianBlur(gray, (5, 5), 0)
    # Edge detection
    edges = cv2.Canny(blur, S2_CANNY_LOW, S2_CANNY_HIGH)
    # Close small gaps
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (S2_MORPH_KERNEL, S2_MORPH_KERNEL))
    edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

    debug["doc_edges"] = edges_closed

    # Find contours
    cnts, _ = cv2.findContours(edges_closed, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key=cv2.contourArea, reverse=True)

    doc_quad = None
    h, w = gray.shape
    img_area = h * w

    for c in cnts[:10]:
        area = cv2.contourArea(c)
        if area < img_area * float(S2_MIN_AREA_FRACTION):
            # Too small to be the page
            continue
        peri = cv2.arcLength(c, True)
        approx = cv2.approxPolyDP(c, float(S2_APPROX_EPS_RATIO) * peri, True)
        if len(approx) == 4 and cv2.isContourConvex(approx):
            doc_quad = approx.reshape(4, 2)
            break

    drawn = img.copy()
    if doc_quad is not None:
        cv2.drawContours(drawn, [doc_quad.astype(np.int32)], -1, (0, 255, 0), 3)
    debug["doc_contour"] = drawn

    if doc_quad is None:
        return None, debug

    warped = four_point_warp(img, doc_quad)
    debug["doc_warp"] = warped
    return warped, debug


def rotate_right_angle(gray: np.ndarray, k: int) -> np.ndarray:
    k = k % 4
    if k == 0:
        return gray
    if k == 1:
        return cv2.rotate(gray, cv2.ROTATE_90_CLOCKWISE)
    if k == 2:
        return cv2.rotate(gray, cv2.ROTATE_180)
    return cv2.rotate(gray, cv2.ROTATE_90_COUNTERCLOCKWISE)


def orientation_score(gray: np.ndarray) -> float:
    # Quick binarization then horizontal projection variance as a proxy for text-line alignment
    blur = cv2.GaussianBlur(gray, (3, 3), 0)
    _, th = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    # Assume text is darker -> count black pixels per row
    rows = np.sum(th == 0, axis=1).astype(np.float32)
    # Normalize to robustness
    if rows.size == 0:
        return 0.0
    rows = (rows - rows.mean()) / (rows.std() + 1e-6)
    return float(np.var(rows))


def auto_rotate_by_90(gray: np.ndarray) -> Tuple[np.ndarray, int, float]:
    best_score = -1.0
    best_k = 0
    best_img = gray
    for k in range(4):
        cand = rotate_right_angle(gray, k)
        score = orientation_score(cand)
        if score > best_score:
            best_score = score
            best_k = k
            best_img = cand
    angle = best_k * 90
    return best_img, angle, best_score



def find_images(input_path: Path, recursive: bool) -> List[Path]:
    if input_path.is_file():
        return [input_path]
    paths: List[Path] = []
    all_supported = SUPPORTED_EXTS | (SUPPORTED_PDF_EXTS if PDF_SUPPORT else set())
    if recursive:
        for p in input_path.rglob("*"):
            if p.suffix.lower() in all_supported and p.is_file():
                paths.append(p)
    else:
        for p in input_path.glob("*"):
            if p.suffix.lower() in all_supported and p.is_file():
                paths.append(p)
    return sorted(paths)


def ensure_odd(n: int) -> int:
    return n if n % 2 == 1 else n + 1


def resize_longer_side(img: np.ndarray, max_size: int) -> np.ndarray:
    if max_size <= 0:
        return img
    h, w = img.shape[:2]
    longer = max(h, w)
    if longer <= max_size:
        return img
    scale = max_size / float(longer)
    new_w, new_h = int(round(w * scale)), int(round(h * scale))
    return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)


def to_grayscale(img: np.ndarray) -> np.ndarray:
    if len(img.shape) == 2:
        return img
    return cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)


def correct_illumination(gray: np.ndarray) -> np.ndarray:
    """
    Correct uneven illumination using background subtraction.
    Only applies correction if significant illumination variation is detected.
    """
    h, w = gray.shape

    # Check if illumination correction is actually needed
    # Calculate local mean in different regions
    h_third, w_third = h // 3, w // 3
    regions = [
        gray[0:h_third, 0:w_third],           # top-left
        gray[0:h_third, w_third:2*w_third],   # top-center
        gray[0:h_third, 2*w_third:w],         # top-right
        gray[h_third:2*h_third, 0:w_third],   # middle-left
        gray[h_third:2*h_third, w_third:2*w_third], # center
        gray[h_third:2*h_third, 2*w_third:w], # middle-right
        gray[2*h_third:h, 0:w_third],         # bottom-left
        gray[2*h_third:h, w_third:2*w_third], # bottom-center
        gray[2*h_third:h, 2*w_third:w]        # bottom-right
    ]

    region_means = [np.mean(region) for region in regions if region.size > 0]
    if len(region_means) < 2:
        return gray

    # Check variation in regional brightness
    mean_variation = np.std(region_means)
    if mean_variation < 20:  # Threshold for "significant" variation
        logging.debug(f"Illumination variation ({mean_variation:.1f}) below threshold, skipping correction")
        return gray

    logging.debug(f"Applying illumination correction (variation: {mean_variation:.1f})")

    # Estimate background via large Gaussian blur
    rel = max(0.001, float(S5_BLUR_REL))
    k = max(15, ensure_odd(int(round(max(h, w) * rel))))
    blurred = cv2.GaussianBlur(gray, (k, k), 0)

    # Use safer arithmetic to avoid underflow
    gray_float = gray.astype(np.float32)
    blurred_float = blurred.astype(np.float32)

    # Subtract background but add offset to prevent underflow
    corrected = gray_float - blurred_float + 128

    # Clip to valid range and normalize more conservatively
    corrected = np.clip(corrected, 0, 255)

    # Only normalize if the range is too compressed
    min_val, max_val = corrected.min(), corrected.max()
    if max_val - min_val < 100:  # Range too compressed, expand it
        corrected = cv2.normalize(corrected, None, alpha=0, beta=255, norm_type=cv2.NORM_MINMAX)

    return corrected.astype(np.uint8)


def apply_clahe(gray: np.ndarray, clip: float = 3.0, grid: int = 8) -> np.ndarray:
    clahe = cv2.createCLAHE(clipLimit=float(clip), tileGridSize=(grid, grid))
    return clahe.apply(gray)


def denoise_gray(gray: np.ndarray, h: int = 10) -> np.ndarray:
    h = max(0, int(h))
    if h == 0:
        return gray
    # Non-local means works well for text noise
    return cv2.fastNlMeansDenoising(gray, h=h, templateWindowSize=7, searchWindowSize=21)


def threshold_image(gray: np.ndarray, method: str, block_size: int, C: int,
                    sauvola_k: float, sauvola_R: float) -> np.ndarray:
    method = method.lower()
    if method == "otsu":
        # Use slight blur before Otsu to stabilize
        blur = cv2.GaussianBlur(gray, (0, 0), 1.0)
        _, th = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        return th
    elif method in ("gaussian", "mean"):
        block_size = ensure_odd(max(3, block_size))
        adaptive_method = cv2.ADAPTIVE_THRESH_GAUSSIAN_C if method == "gaussian" else cv2.ADAPTIVE_THRESH_MEAN_C
        th = cv2.adaptiveThreshold(gray, 255, adaptive_method, cv2.THRESH_BINARY, block_size, C)
        return th
    elif method == "sauvola":
        return sauvola_binarize(gray, window=ensure_odd(max(15, block_size)), k=sauvola_k, R=sauvola_R)
    else:
        raise ValueError(f"Unknown threshold method: {method}")


def sauvola_binarize(gray: np.ndarray, window: int = 31, k: float = 0.2, R: float = 128.0) -> np.ndarray:
    # Implementation of Sauvola threshold using local mean and std via box filters
    img = gray.astype(np.float32)
    w = ensure_odd(window)
    mean = cv2.boxFilter(img, ddepth=-1, ksize=(w, w), normalize=True)
    sqmean = cv2.boxFilter(img * img, ddepth=-1, ksize=(w, w), normalize=True)
    var = np.maximum(sqmean - mean * mean, 0.0)
    std = np.sqrt(var)
    # Sauvola threshold
    thresh = mean * (1 + k * ((std / max(R, 1e-6)) - 1))
    # Binarize
    binary = (img > thresh).astype(np.uint8) * 255
    return binary


def morphology(binary: np.ndarray, k_open: int = 1, k_close: int = 1, k_median: int = 3) -> np.ndarray:
    out = binary
    if k_open and k_open > 0:
        k = cv2.getStructuringElement(cv2.MORPH_RECT, (k_open, k_open))
        out = cv2.morphologyEx(out, cv2.MORPH_OPEN, k)
    if k_close and k_close > 0:
        k = cv2.getStructuringElement(cv2.MORPH_RECT, (k_close, k_close))
        out = cv2.morphologyEx(out, cv2.MORPH_CLOSE, k)
    # Median filter as final step to remove small spots/noise
    if k_median and k_median > 0:
        k_median = k_median if k_median % 2 == 1 else k_median + 1  # ensure odd
        out = cv2.medianBlur(out, k_median)
    return out


def estimate_skew_angle(binary: np.ndarray) -> float:
    """
    Estimate skew angle from binary image using Hough line detection.
    Works better on clean binary images than grayscale.
    """
    # For binary images, text should be black (0) on white (255) background
    # If text is white on black, invert
    if np.mean(binary) < 127:  # More black pixels than white - invert
        working_img = cv2.bitwise_not(binary)
    else:
        working_img = binary.copy()

    # Use the binary image directly for Hough line detection
    # No need for Canny edge detection on clean binary images
    thr = max(S10_HOUGH_THRESHOLD_MIN, int(float(S10_HOUGH_THRESHOLD_REL) * binary.shape[0]))
    lines = cv2.HoughLines(working_img, 1, np.pi / 180.0, threshold=thr)

    if lines is None:
        logging.debug("No Hough lines detected for skew estimation")
        return 0.0

    angles = []
    for rho_theta in lines[:50]:  # Reduced from 200 to 50 for better performance
        rho, theta = rho_theta[0]

        # Convert theta from radians to degrees
        angle_deg = theta * 180.0 / math.pi

        # Hough lines give angles from 0 to 180 degrees
        # For horizontal text lines, we expect angles close to 0 or 180
        # Convert to skew angle in range [-90, 90]
        if angle_deg > 90:
            skew_angle = angle_deg - 180
        else:
            skew_angle = angle_deg

        # Filter for nearly horizontal lines (small skew angles)
        # Text lines should have small skew, typically < 15 degrees
        if abs(skew_angle) <= 15:
            angles.append(skew_angle)

    if not angles:
        logging.debug("No suitable horizontal lines found for skew estimation")
        return 0.0

    # Use median for robustness
    estimated_angle = float(np.median(angles))
    logging.debug(f"Estimated skew angle: {estimated_angle:.2f}° from {len(angles)} lines")
    return estimated_angle


def rotate_image(img: np.ndarray, angle_deg: float) -> np.ndarray:
    if abs(angle_deg) < 0.3:
        return img
    h, w = img.shape[:2]
    center = (w / 2.0, h / 2.0)
    M = cv2.getRotationMatrix2D(center, angle_deg, 1.0)
    cos = abs(M[0, 0])
    sin = abs(M[0, 1])
    # Compute new bounding dimensions
    nW = int((h * sin) + (w * cos))
    nH = int((h * cos) + (w * sin))
    # Adjust matrix to account for translation
    M[0, 2] += (nW / 2) - center[0]
    M[1, 2] += (nH / 2) - center[1]
    return cv2.warpAffine(img, M, (nW, nH), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REPLICATE)


def preprocess_for_ocr(img_bgr: np.ndarray, opt: Options, debug_store: Dict[str, np.ndarray] | None = None) -> np.ndarray:
    dbg = debug_store if opt.save_debug and debug_store is not None else None

    # SECTION 1: DPI normalization to ~300 DPI
    if S1_ENABLE:
        img = dpi_normalize(img_bgr)
    else:
        img = img_bgr
    if dbg is not None:
        dbg["01_dpi_normalize"] = img

    # SECTION 2: Document detection + perspective warp
    if S2_ENABLE:
        warped, doc_dbg = detect_document_and_warp(img)
        if dbg is not None:
            for k, v in doc_dbg.items():
                dbg[f"02_{k}"] = v
        if warped is not None:
            img = warped
            if dbg is not None:
                dbg["02_doc_warp"] = img

    # SECTION 3 and 4: Auto-orient + Grayscale
    gray = to_grayscale(img) if S4_ENABLE else to_grayscale(img)
    if dbg is not None:
        dbg["03_gray_before_orient"] = gray

    if S3_ENABLE:
        oriented, rot_angle, rot_score = auto_rotate_by_90(gray)
        gray = oriented
        if dbg is not None:
            dbg["03_auto_orient_angle"] = np.array([[rot_angle, rot_score]], dtype=np.float32)
            dbg["03_auto_orient"] = gray

    # SECTION 5: Illumination correction
    if S5_ENABLE:
        gray = correct_illumination(gray)
        if dbg is not None:
            dbg["05_illum"] = gray

    # SECTION 6: CLAHE
    if S6_ENABLE:
        gray = apply_clahe(gray, clip=float(S6_CLAHE_CLIP), grid=int(S6_CLAHE_GRID))
        if dbg is not None:
            dbg["06_clahe"] = gray

    # SECTION 7: Denoise
    if S7_ENABLE:
        gray = denoise_gray(gray, h=int(S7_STRENGTH))
        if dbg is not None:
            dbg["07_denoise"] = gray

    # SECTION 8: Binarization
    if S8_ENABLE and S8_METHOD and S8_METHOD != "none":
        binary = threshold_image(gray, S8_METHOD, int(S8_BLOCK_SIZE), int(S8_C), float(S8_SAUVOLA_K), float(S8_SAUVOLA_R))
        if dbg is not None:
            dbg["08_binary"] = binary
    else:
        binary = gray
        if dbg is not None:
            dbg["08_binary_skipped"] = binary

    # SECTION 9: Morphology
    if S9_ENABLE and (int(S9_OPEN) > 0 or int(S9_CLOSE) > 0 or int(S9_MEDIAN) > 0):
        binary = morphology(binary, k_open=int(S9_OPEN), k_close=int(S9_CLOSE), k_median=int(S9_MEDIAN))
        if dbg is not None:
            dbg["09_morph"] = binary
    else:
        if dbg is not None:
            dbg["09_morph_skipped"] = binary

    # SECTION 10: Fine deskew (MOVED TO END - works better on clean binary image)
    if S10_ENABLE:
        angle = estimate_skew_angle(binary)
        if abs(angle) >= float(S10_MIN_ABS_ANGLE):
            binary = rotate_image(binary, angle)
            if dbg is not None:
                dbg["10_deskew_angle"] = np.array([[angle]], dtype=np.float32)
                dbg["10_deskew"] = binary

    return binary


def read_image(path: Path) -> np.ndarray | None:
    img = cv2.imread(str(path), cv2.IMREAD_COLOR)
    if img is None:
        logging.warning(f"Failed to read image: {path}")
    return img


def read_pdf_pages(path: Path, dpi: int = 200) -> List[np.ndarray]:
    """Convert PDF pages to images using pdf2image."""
    if not PDF_SUPPORT:
        logging.error(f"PDF support not available. Cannot process: {path}")
        return []

    try:
        # Convert PDF to PIL images
        pil_images = convert_from_path(str(path), dpi=dpi)
        cv_images = []

        for pil_img in pil_images:
            # Convert PIL image to OpenCV format (BGR)
            cv_img = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
            cv_images.append(cv_img)

        logging.info(f"Converted PDF {path} to {len(cv_images)} page(s)")
        return cv_images

    except Exception as e:
        logging.error(f"Failed to read PDF {path}: {e}")
        return []


def write_image(path: Path, img: np.ndarray) -> None:
    path.parent.mkdir(parents=True, exist_ok=True)
    ok = cv2.imwrite(str(path), img)
    if not ok:
        raise IOError(f"Failed to write image: {path}")


def detect_orientation_with_tesseract(img: np.ndarray) -> dict:
    """
    Detect page orientation using Tesseract OSD (Orientation and Script Detection).

    Returns:
        Dictionary with orientation info or empty dict if detection fails
    """
    if not TESSERACT_SUPPORT:
        return {}

    try:
        # Use PSM 0 for orientation detection only
        osd = pytesseract.image_to_osd(img, output_type=pytesseract.Output.DICT)
        logging.debug(f"Detected orientation: {osd.get('orientation', 'unknown')}°, "
                     f"confidence: {osd.get('orientation_conf', 0):.1f}")
        return osd
    except Exception as e:
        logging.debug(f"Orientation detection failed: {e}")
        return {}


def extract_text_with_tesseract(img: np.ndarray, lang: str = "ces+eng", psm: int = 6, oem: int = 3,
                               auto_orient: bool = True) -> str:
    """
    Extract text from preprocessed binary image using Tesseract OCR.

    Args:
        img: Binary image (preprocessed for OCR)
        lang: Language codes for Tesseract (e.g., "ces+eng" for Czech and English)
        psm: Page segmentation mode
        oem: OCR Engine Mode
        auto_orient: Try to detect and correct orientation automatically

    Returns:
        Extracted text as string
    """
    if not TESSERACT_SUPPORT:
        logging.warning("Tesseract not available, skipping OCR")
        return ""

    try:
        working_img = img.copy()

        # Try orientation detection first if enabled
        if auto_orient:
            osd = detect_orientation_with_tesseract(working_img)
            if osd and 'orientation' in osd:
                orientation = osd['orientation']
                confidence = osd.get('orientation_conf', 0)

                # Only rotate if confidence is reasonable and angle is significant
                if confidence > 1.0 and orientation != 0:
                    logging.info(f"Auto-rotating image by {orientation}° (confidence: {confidence:.1f})")
                    if orientation == 90:
                        working_img = cv2.rotate(working_img, cv2.ROTATE_90_COUNTERCLOCKWISE)
                    elif orientation == 180:
                        working_img = cv2.rotate(working_img, cv2.ROTATE_180)
                    elif orientation == 270:
                        working_img = cv2.rotate(working_img, cv2.ROTATE_90_CLOCKWISE)

        # Configure Tesseract for text extraction
        # Use PSM 1 (auto page segmentation with OSD) if auto_orient is enabled
        actual_psm = 1 if auto_orient else psm
        config = f'--oem {oem} --psm {actual_psm}'

        # Extract text
        text = pytesseract.image_to_string(working_img, lang=lang, config=config)

        # Clean up the text
        text = text.strip()

        # Log some basic stats
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        logging.debug(f"OCR extracted {len(lines)} non-empty lines, {len(text)} characters total")

        return text

    except Exception as e:
        logging.error(f"OCR extraction failed: {e}")
        return ""


def write_text_file(path: Path, text: str) -> None:
    """Write text to file with UTF-8 encoding."""
    path.parent.mkdir(parents=True, exist_ok=True)
    try:
        with open(path, 'w', encoding='utf-8') as f:
            f.write(text)
    except Exception as e:
        logging.error(f"Failed to write text file {path}: {e}")
        raise


def process_path(input_path: Path, output_dir: Path, recursive: bool, opt: Options) -> None:
    images = find_images(input_path, recursive=recursive)
    if not images:
        logging.error("No images found.")
        return
    logging.info(f"Found {len(images)} image(s)")

    for idx, img_path in enumerate(images, 1):
        logging.info(f"[{idx}/{len(images)}] Processing: {img_path}")

        # Handle PDF files differently
        if img_path.suffix.lower() == ".pdf":
            if not PDF_SUPPORT:
                logging.warning(f"Skipping PDF file {img_path} - pdf2image not available")
                continue

            pdf_pages = read_pdf_pages(img_path, dpi=PDF_DPI)
            if not pdf_pages:
                continue

            # Process each page of the PDF
            for page_idx, img in enumerate(pdf_pages, 1):
                debug_store: Dict[str, np.ndarray] = {}
                out = preprocess_for_ocr(img, opt, debug_store=debug_store)

                rel = img_path.name
                base_name = f"{Path(rel).stem}_page{page_idx:03d}"
                out_name = f"{base_name}_ocr.png"
                out_path = output_dir / out_name
                write_image(out_path, out)
                logging.info(f"Saved: {out_path}")

                # OCR text extraction
                if OCR_ENABLE and TESSERACT_SUPPORT:
                    text = extract_text_with_tesseract(out, lang=OCR_LANG, psm=OCR_PSM, oem=OCR_OEM, auto_orient=OCR_AUTO_ORIENT)
                    if text:
                        text_name = f"{base_name}_text.txt"
                        text_path = output_dir / text_name
                        write_text_file(text_path, text)
                        logging.info(f"Saved OCR text: {text_path}")
                    else:
                        logging.warning(f"No text extracted from {img_path} page {page_idx}")

                if opt.save_debug:
                    dbg_dir = output_dir / f"{base_name}_debug"
                    dbg_dir.mkdir(parents=True, exist_ok=True)
                    for key, val in debug_store.items():
                        # Skip non-image debug entries
                        if val.ndim == 2 or (val.ndim == 3 and val.shape[2] in (1, 3, 4)):
                            dbg_path = dbg_dir / f"{key}.png"
                            cv2.imwrite(str(dbg_path), val)
                        elif key.endswith("angle"):
                            # Write angle as text file
                            (dbg_dir / f"{key}.txt").write_text(str(val.flatten()[0]))
        else:
            # Handle regular image files
            img = read_image(img_path)
            if img is None:
                continue

            debug_store: Dict[str, np.ndarray] = {}
            out = preprocess_for_ocr(img, opt, debug_store=debug_store)

            rel = img_path.name  # keep flat; could also preserve tree with relative_to
            base_name = Path(rel).stem
            out_name = f"{base_name}_ocr.png"
            out_path = output_dir / out_name
            write_image(out_path, out)
            logging.info(f"Saved: {out_path}")

            # OCR text extraction
            if OCR_ENABLE and TESSERACT_SUPPORT:
                text = extract_text_with_tesseract(out, lang=OCR_LANG, psm=OCR_PSM, oem=OCR_OEM, auto_orient=OCR_AUTO_ORIENT)
                if text:
                    text_name = f"{base_name}_text.txt"
                    text_path = output_dir / text_name
                    write_text_file(text_path, text)
                    logging.info(f"Saved OCR text: {text_path}")
                else:
                    logging.warning(f"No text extracted from {img_path}")

            if opt.save_debug:
                dbg_dir = output_dir / f"{base_name}_debug"
                dbg_dir.mkdir(parents=True, exist_ok=True)
                for key, val in debug_store.items():
                    # Skip non-image debug entries
                    if val.ndim == 2 or (val.ndim == 3 and val.shape[2] in (1, 3, 4)):
                        dbg_path = dbg_dir / f"{key}.png"
                        cv2.imwrite(str(dbg_path), val)
                    elif key.endswith("angle"):
                        # Write angle as text file
                        (dbg_dir / f"{key}.txt").write_text(str(val.flatten()[0]))


def build_arg_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description="Preprocess images for OCR using OpenCV")
    p.add_argument("-i", "--input", required=False, default=DEFAULT_INPUT, type=str, help="Input file or directory")
    p.add_argument("-o", "--output-dir", default=DEFAULT_OUTPUT_DIR, type=str, help="Directory to save processed images")
    p.add_argument("-r", "--recursive", action="store_true", default=DEFAULT_RECURSIVE, help="Recursively search for images in a directory")

    # Geometric toggles
    p.add_argument("--no-detect-document", action="store_true", help="Disable document detection and perspective warp")
    p.add_argument("--no-auto-orient", action="store_true", help="Disable auto 90-degree orientation")
    p.add_argument("--no-deskew", action="store_true", help="Disable deskew")
    p.add_argument("--max-size", type=int, default=DEFAULT_MAX_SIZE, help="Resize longer side to this many pixels (0=disable)")

    # Photometric toggles
    p.add_argument("--no-illum", action="store_true", help="Disable illumination correction")
    p.add_argument("--no-clahe", action="store_true", help="Disable CLAHE contrast enhancement")
    p.add_argument("--clahe-clip", type=float, default=DEFAULT_CLAHE_CLIP, help="CLAHE clip limit")
    p.add_argument("--clahe-grid", type=int, default=DEFAULT_CLAHE_GRID, help="CLAHE tile grid size")
    p.add_argument("--no-denoise", action="store_true", help="Disable denoising")
    p.add_argument("--denoise-strength", type=int, default=DEFAULT_DENOISE_STRENGTH, help="Denoising strength (h)")

    # Binarization and morphology
    p.add_argument("--method", choices=["none", "gaussian", "mean", "otsu", "sauvola"], default=DEFAULT_METHOD,
                   help="Binarization method; 'none' skips thresholding")
    p.add_argument("--block-size", type=int, default=DEFAULT_BLOCK_SIZE, help="Neighborhood size (odd) for adaptive methods")
    p.add_argument("-C", type=int, default=DEFAULT_C, help="Subtracted constant for adaptive thresholding")
    p.add_argument("--sauvola-k", type=float, default=DEFAULT_SAUVOLA_K, help="Sauvola k parameter")
    p.add_argument("--sauvola-R", type=float, default=DEFAULT_SAUVOLA_R, help="Sauvola R parameter")

    p.add_argument("--open", type=int, default=DEFAULT_OPEN, help="Morphological opening kernel size (0=disable)")
    p.add_argument("--close", type=int, default=DEFAULT_CLOSE, help="Morphological closing kernel size (0=disable)")
    p.add_argument("--median", type=int, default=3, help="Median filter kernel size (0=disable, must be odd)")
    p.add_argument("--no-morph", action="store_true", help="Disable morphology (sets open=close=median=0)")

    # OCR settings
    p.add_argument("--no-ocr", action="store_true", help="Disable OCR text extraction")
    p.add_argument("--ocr-lang", type=str, default="ces+eng", help="Tesseract language codes (e.g., ces+eng)")
    p.add_argument("--ocr-psm", type=int, default=6, help="Page segmentation mode (6=uniform block of text)")
    p.add_argument("--ocr-oem", type=int, default=3, help="OCR Engine Mode (3=default)")
    p.add_argument("--no-auto-orient", action="store_true", help="Disable automatic orientation detection")

    # Debug/logging
    p.add_argument("--save-debug", action="store_true", help="Save intermediate steps for debugging")
    p.add_argument("-v", "--verbose", action="store_true", help="Verbose logging")
    return p


def main(argv: List[str] | None = None) -> None:
    if RUN_WITH_CLI:
        parser = build_arg_parser()
        args = parser.parse_args(argv)

        logging.basicConfig(level=logging.DEBUG if getattr(args, 'verbose', False) else logging.INFO, format="%(message)s")

        input_path = Path(getattr(args, 'input', DEFAULT_INPUT))
        output_dir = Path(getattr(args, 'output_dir', DEFAULT_OUTPUT_DIR))

        opt = Options(
            deskew=not getattr(args, 'no_deskew', False),
            max_size=getattr(args, 'max_size', DEFAULT_MAX_SIZE),
            illumination_correction=not getattr(args, 'no_illum', False),
            clahe=not getattr(args, 'no_clahe', False),
            clahe_clip=getattr(args, 'clahe_clip', DEFAULT_CLAHE_CLIP),
            clahe_grid=getattr(args, 'clahe_grid', DEFAULT_CLAHE_GRID),
            denoise=not getattr(args, 'no_denoise', False),
            denoise_strength=getattr(args, 'denoise_strength', DEFAULT_DENOISE_STRENGTH),
            threshold_method=getattr(args, 'method', DEFAULT_METHOD),
            block_size=getattr(args, 'block_size', DEFAULT_BLOCK_SIZE),
            C=getattr(args, 'C', DEFAULT_C),
            sauvola_k=getattr(args, 'sauvola_k', DEFAULT_SAUVOLA_K),
            sauvola_R=getattr(args, 'sauvola_R', DEFAULT_SAUVOLA_R),
            morph_open=(0 if getattr(args, 'no_morph', False) else getattr(args, 'open', DEFAULT_OPEN)),
            morph_close=(0 if getattr(args, 'no_morph', False) else getattr(args, 'close', DEFAULT_CLOSE)),
            morph_median=(0 if getattr(args, 'no_morph', False) else getattr(args, 'median', 3)),
            save_debug=getattr(args, 'save_debug', DEFAULT_SAVE_DEBUG),
            detect_document=not getattr(args, 'no_detect_document', False),
            auto_orient=not getattr(args, 'no_auto_orient', False),
        )

        process_path(input_path, output_dir, recursive=getattr(args, 'recursive', DEFAULT_RECURSIVE), opt=opt)
        return

    # IDE mode: take everything from USER CONFIG
    logging.basicConfig(level=logging.DEBUG if VERBOSE else logging.INFO, format="%(message)s")

    input_path = Path(INPUT_PATH)
    output_dir = Path(OUTPUT_DIR)

    opt = Options(
        deskew=S10_ENABLE,  # Updated to S10 (deskew moved to end)
        max_size=0,  # DPI normalization supersedes simple resize
        illumination_correction=S5_ENABLE,
        clahe=S6_ENABLE,
        clahe_clip=float(S6_CLAHE_CLIP),
        clahe_grid=int(S6_CLAHE_GRID),
        denoise=S7_ENABLE,
        denoise_strength=int(S7_STRENGTH),
        threshold_method=S8_METHOD,  # Updated to S8 (binarization moved up)
        block_size=int(S8_BLOCK_SIZE),
        C=int(S8_C),
        sauvola_k=float(S8_SAUVOLA_K),
        sauvola_R=float(S8_SAUVOLA_R),
        morph_open=int(S9_OPEN) if S9_ENABLE else 0,  # Updated to S9 (morphology moved up)
        morph_close=int(S9_CLOSE) if S9_ENABLE else 0,
        morph_median=int(S9_MEDIAN) if S9_ENABLE else 0,
        save_debug=SAVE_DEBUG,
        detect_document=S2_ENABLE,
        auto_orient=S3_ENABLE,
    )

    process_path(Path(INPUT_PATH), Path(OUTPUT_DIR), recursive=RECURSIVE, opt=opt)


if __name__ == "__main__":
    main()

